{"buildFiles": ["/opt/homebrew/Caskroom/flutter/3.22.1/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/Ya<PERSON><PERSON>/WiinekApp/wiinekuser/android/app/.cxx/Debug/4t2ihy5x/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/Ya<PERSON><PERSON>/WiinekApp/wiinekuser/android/app/.cxx/Debug/4t2ihy5x/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}