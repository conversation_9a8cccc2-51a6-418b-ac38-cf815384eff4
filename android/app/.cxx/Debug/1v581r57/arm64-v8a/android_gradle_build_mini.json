{"buildFiles": ["/opt/homebrew/Caskroom/flutter/3.22.1/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Downloads/WiinekApp/flutter_user/flutter_user/android/app/.cxx/Debug/1v581r57/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Downloads/WiinekApp/flutter_user/flutter_user/android/app/.cxx/Debug/1v581r57/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}